import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:panchang_at_this_moment/components/map_selection.dart';
import 'package:panchang_at_this_moment/components/time_control_widget.dart';
import 'package:panchang_at_this_moment/main.dart';
import 'package:panchang_at_this_moment/panchang_at_this_moment.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  DateTime _startTime =
      DateTime.now().add(const Duration(days: 0, hours: 0, minutes: 0));
  double? _lat;
  double? _lng;
  final double _alt = 0.494;

  // Time control state
  DateTime _currentTime = DateTime.now();
  bool _isPlaying = true;

  @override
  void initState() {
    super.initState();
    _loadLocation();
    _currentTime = _startTime;
  }

  void _onTimeChanged(DateTime newTime) {
    setState(() {
      _currentTime = newTime;
    });
  }

  void _onPlayStateChanged(bool isPlaying) {
    setState(() {
      _isPlaying = isPlaying;
    });
  }

  void _togglePlayPause() {
    setState(() {
      _isPlaying = !_isPlaying;
    });
  }

  void _resetToNow() {
    setState(() {
      _currentTime = DateTime.now();
      _startTime = _currentTime;
    });
  }

  Future<void> _loadLocation() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _lat = prefs.getDouble('lat') ?? 22.7196; // Default to Indore, India
      _lng = prefs.getDouble('lng') ?? 75.8577; // Default to Indore, India
    });
  }

  Future<void> _getCurrentLocation() async {
    _showLocationMessage('Getting your location...');

    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        _showLocationMessage('Location services are disabled. Please enable GPS and try again.');
        return;
      }

      // Check location permission
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          _showLocationMessage('Location permission denied. Using current location.');
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        _showLocationMessage('Location permission permanently denied. Please enable it in settings.');
        return;
      }

      // Get current position
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 15),
      );

      // Update location
      setState(() {
        _lat = position.latitude;
        _lng = position.longitude;
      });

      // Save location to preferences
      _saveLocation(_lat!, _lng!);

      _showLocationMessage('Location updated successfully! (${position.latitude.toStringAsFixed(4)}, ${position.longitude.toStringAsFixed(4)})');

    } catch (e) {
      debugPrint('Location error: $e');
      _showLocationMessage('Could not get current location: ${e.toString().split('.').last}. Using saved location.');
    }
  }

  void _showLocationMessage(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: const Color(0xFF6B4E3D),
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  void _saveLocation(double lat, double lng) async {
    final prefs = await SharedPreferences.getInstance();
    prefs.setDouble('lat', lat);
    prefs.setDouble('lng', lng);
  }

  void _selectLocation() {
    if (_lat == null || _lng == null) return;
    Navigator.push(
      context,
      MaterialPageRoute(
          builder: (context) => MapSelectionScreen(
                onLocationSelected: (location) {
                  setState(() {
                    _lat = location[0].toDouble();
                    _lng = location[1].toDouble();
                  });
                  _saveLocation(_lat!, _lng!);
                  Navigator.of(context).pop();
                },
                initialLat: _lat!,
                initialLng: _lng!,
              )),
    );
  }

  void _selectTime(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _startTime,
      firstDate: DateTime(1600),
      lastDate: DateTime(2400),
    );

    if (picked != null) {
      final TimeOfDay? timeOfDay = await showTimePicker(
        // ignore: use_build_context_synchronously
        context: context,
        initialTime: TimeOfDay.fromDateTime(_startTime),
      );

      if (timeOfDay != null) {
        setState(() {
          _startTime = DateTime(
            picked.year,
            picked.month,
            picked.day,
            timeOfDay.hour,
            timeOfDay.minute,
          );
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            Image.asset(
              'assets/panchang_logo.jpg',
              width: 32,
              height: 32,
            ),
            const SizedBox(width: 10),
            Text(AppLocalizations.of(context)!.appTitle,
                style: const TextStyle(
                  fontSize: 26,
                  fontWeight: FontWeight.bold,
                )),
          ],
        ),
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(80),
          child: TimeControlWidget(
            currentTime: _currentTime,
            isPlaying: _isPlaying,
            onPlayPause: _togglePlayPause,
            onReset: _resetToNow,
          ),
        ),
        actions: [
          Builder(
            builder: (context) {
              return PopupMenuButton<String>(
                icon: const Icon(Icons.settings),
                onSelected: (value) {
                  switch (value) {
                    case 'selectTime':
                      _selectTime(context);
                      break;
                    case 'selectLocation':
                      _selectLocation();
                      break;
                    case 'getCurrentLocation':
                      _getCurrentLocation();
                      break;
                    case 'selectLanguage':
                      FocusScope.of(context).unfocus();
                      showDialog<void>(
                        context: context,
                        builder: (context) {
                          return AlertDialog(
                            title: const Text('Select Language'),
                            content: Wrap(
                              spacing: 8,
                              runSpacing: 8,
                              children: {
                                'en': 'English',
                                'hi': 'हिन्दी',
                                'te': 'తెలుగు',
                                'ta': 'தமிழ்',
                                'kn': 'ಕನ್ನಡ',
                                'ml': 'മലയാളം',
                                'be': 'বাংলা',
                                'mr': 'मराठी',
                              }.entries.map<Widget>((entry) {
                                return ElevatedButton(
                                  onPressed: () {
                                    MyApp.of(context)?.setLocale(
                                        Locale.fromSubtags(
                                            languageCode: entry.key));
                                    Navigator.of(context).pop();
                                  },
                                  child: Text(entry.value),
                                );
                              }).toList(),
                            ),
                          );
                        },
                      );
                      break;
                  }
                },
                itemBuilder: (context) {
                  return <PopupMenuItem<String>>[
                    const PopupMenuItem<String>(
                      value: 'selectTime',
                      child: Row(
                        children: [
                          Icon(
                            Icons.access_time_rounded,
                            color: Colors.orange,
                          ),
                          SizedBox(width: 8),
                          Text(
                            'Change Time',
                          ),
                        ],
                      ),
                    ),
                    const PopupMenuItem<String>(
                      value: 'selectLocation',
                      child: Row(
                        children: [
                          Icon(
                            Icons.location_on_rounded,
                            color: Colors.green,
                          ),
                          SizedBox(width: 8),
                          Text(
                            'Change Location',
                          ),
                        ],
                      ),
                    ),
                    const PopupMenuItem<String>(
                      value: 'getCurrentLocation',
                      child: Row(
                        children: [
                          Icon(
                            Icons.my_location_rounded,
                            color: Colors.teal,
                          ),
                          SizedBox(width: 8),
                          Text(
                            'Use Current Location',
                          ),
                        ],
                      ),
                    ),
                    const PopupMenuItem<String>(
                      value: 'selectLanguage',
                      child: Row(
                        children: [
                          Icon(
                            Icons.language_rounded,
                            color: Colors.blue,
                          ),
                          SizedBox(width: 8),
                          Text(
                            'Change Language',
                          ),
                        ],
                      ),
                    ),
                  ];
                },
              );
            },
          ),
        ],
      ),
      body: (_lat == null || _lng == null)
          ? const Center(child: CircularProgressIndicator())
          : PanchangAtTheMoment(
              key: Key('$_startTime, $_lat, $_lng'),
              startTime: _currentTime,
              lat: _lat!,
              lng: _lng!,
              alt: _alt,
              onTimeChanged: _onTimeChanged,
              onPlayStateChanged: _onPlayStateChanged,
            ),
    );
  }
}
