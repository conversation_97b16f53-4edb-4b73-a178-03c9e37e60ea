import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class TimeControlWidget extends StatelessWidget {
  final DateTime currentTime;
  final bool isPlaying;
  final VoidCallback onPlayPause;
  final VoidCallback onReset;

  const TimeControlWidget({
    super.key,
    required this.currentTime,
    required this.isPlaying,
    required this.onPlayPause,
    required this.onReset,
  });

  String _getLocalizedDayName(BuildContext context, DateTime dateTime) {
    final locale = Localizations.localeOf(context);
    final dayFormat = DateFormat.EEEE(locale.toString());
    return dayFormat.format(dateTime);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 12),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Colors.white, Color(0xFFFAFAFA)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 10,
            spreadRadius: 0,
            offset: const Offset(0, 3),
          ),
        ],
        border: Border.all(color: const Color(0xFFE0E0E0), width: 0.5),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: const Color(0xFFF3E5AB),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.access_time_rounded,
                  color: Color(0xFF6B4E3D),
                  size: 18,
                ),
              ),
              const SizedBox(width: 8),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    DateFormat('dd-MM-yyyy HH:mm:ss').format(currentTime),
                    style: Theme.of(context).textTheme.displayMedium?.copyWith(
                      color: const Color(0xFF3E2723),
                      fontWeight: FontWeight.w600,
                      fontSize: 18,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    _getLocalizedDayName(context, currentTime),
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: const Color(0xFF6B4E3D),
                      fontWeight: FontWeight.w500,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ],
          ),
          Row(
            children: [
              Container(
                decoration: BoxDecoration(
                  color: isPlaying ? const Color(0xFFFFEBEE) : const Color(0xFFE8F5E8),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: IconButton(
                  constraints: const BoxConstraints(minWidth: 44, minHeight: 44),
                  padding: const EdgeInsets.all(8),
                  onPressed: onPlayPause,
                  icon: Icon(
                    isPlaying
                        ? Icons.pause_circle_filled_rounded
                        : Icons.play_circle_fill_rounded,
                    color: isPlaying ? const Color(0xFFD32F2F) : const Color(0xFF388E3C),
                    size: 24,
                  ),
                ),
              ),
              const SizedBox(width: 4),
              Container(
                decoration: BoxDecoration(
                  color: const Color(0xFFF3E5AB),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: IconButton(
                  constraints: const BoxConstraints(minWidth: 44, minHeight: 44),
                  padding: const EdgeInsets.all(8),
                  icon: const Icon(
                    Icons.refresh_rounded,
                    color: Color(0xFF6B4E3D),
                    size: 22,
                  ),
                  onPressed: onReset,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
