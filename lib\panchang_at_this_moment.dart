import 'dart:async';

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:panchang_at_this_moment/API/panchang_api.dart';
import 'package:panchang_at_this_moment/components/panchang_item_always_change.dart';
import 'package:panchang_at_this_moment/components/panchang_item_sometime_change.dart';
import 'package:panchang_at_this_moment/components/static_panchang_item.dart';

class PanchangAtTheMoment extends StatefulWidget {
  final DateTime startTime;
  final double lat;
  final double lng;
  final double alt;
  final Function(DateTime)? onTimeChanged;
  final Function(bool)? onPlayStateChanged;
  const PanchangAtTheMoment({
    super.key,
    required this.startTime,
    required this.lat,
    required this.lng,
    required this.alt,
    this.onTimeChanged,
    this.onPlayStateChanged,
  });

  @override
  State<PanchangAtTheMoment> createState() => _PanchangAtTheMomentState();
}

class _PanchangAtTheMomentState extends State<PanchangAtTheMoment> {
  PanchangDataForThreeDays? _panchangData;
  PanchangDataForThreeDays? translatedPanchangData;
  bool _isPlaying = true;
  late DateTime _currentTime = widget.startTime;
  late Timer _timer;
  late String currentTranslatedLanguage = Localizations.localeOf(context).languageCode;

  void updateFromParent(DateTime newTime, bool newPlayState) {
    setState(() {
      _currentTime = newTime;
      _isPlaying = newPlayState;
    });
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  @override
  void didUpdateWidget(PanchangAtTheMoment oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.startTime != oldWidget.startTime) {
      setState(() {
        _currentTime = widget.startTime;
      });
    }
  }

  @override
  void initState() {
    super.initState();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_isPlaying) {
        setState(() {
          _currentTime = _currentTime.add(const Duration(seconds: 1));
        });
        widget.onTimeChanged?.call(_currentTime);
      }
    });
    _loadPanchangData();
  }

  Future<void> _loadPanchangData() async {
    final DateTime previousDay = widget.startTime.subtract(const Duration(days: 1));
    final DateTime nextDay = widget.startTime.add(const Duration(days: 1));

    final List<Future<PanchangData>> futures = [
      PanchangAPI.getPanchangData(previousDay, widget.lat, widget.lng, widget.alt),
      PanchangAPI.getPanchangData(widget.startTime, widget.lat, widget.lng, widget.alt),
      PanchangAPI.getPanchangData(nextDay, widget.lat, widget.lng, widget.alt),
    ];

    final List<PanchangData> data = await Future.wait(futures);

    setState(() {
      _panchangData = PanchangDataForThreeDays(
        previousDay: data[0],
        currentDay: data[1],
        nextDay: data[2],
      );
      translatedPanchangData = PanchangDataForThreeDays(
        previousDay: _panchangData!.previousDay.translate(Localizations.localeOf(context).languageCode),
        currentDay: _panchangData!.currentDay.translate(Localizations.localeOf(context).languageCode),
        nextDay: _panchangData!.nextDay.translate(Localizations.localeOf(context).languageCode),
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return panchangInfoBox(context, _currentTime);
  }

  Widget panchangInfoBox(BuildContext context, DateTime currentTime) {
    final TextStyle titleTextStyle = Theme.of(context).textTheme.titleLarge!;
    final TextStyle smallTextStyle = Theme.of(context).textTheme.bodySmall!;
    
    // translation part:
    if (_panchangData != null && currentTranslatedLanguage != Localizations.localeOf(context).languageCode) {
      translatedPanchangData = PanchangDataForThreeDays(
        previousDay: _panchangData!.previousDay.translate(Localizations.localeOf(context).languageCode), 
        currentDay: _panchangData!.currentDay.translate(Localizations.localeOf(context).languageCode), 
        nextDay: _panchangData!.nextDay.translate(Localizations.localeOf(context).languageCode)
      );
      currentTranslatedLanguage = Localizations.localeOf(context).languageCode;
    }

    final Widget? panchangSomeTimeChangeBox = (translatedPanchangData != null)
        ? getPanchangItemSomeTimeChange(
            panchangData: translatedPanchangData!,
            currentTime: currentTime,
            titleTextStyle: titleTextStyle,
            smallTextStyle: smallTextStyle,
            context: context)
        : null;

    if (translatedPanchangData == null) {
      return Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFFF3E5AB), // Warm cream color
              Colors.white,
            ],
          ),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const SizedBox(
                width: 50,
                height: 50,
                child: CircularProgressIndicator(
                  color: Color(0xFF6B4E3D),
                  strokeWidth: 3,
                ),
              ),
              const SizedBox(height: 20),
              Text(
                "Loading Panchang data...",
                style: GoogleFonts.mallanna(
                  color: const Color(0xFF5D4037),
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      );
    }

    // Get all items to display
    final staticItems = getStaticPanchangItem(
      translatedPanchangData,
      DateTime(_currentTime.year, _currentTime.month, _currentTime.day),
      context,
    );

    final dynamicItems = getPanchangItemAlwayChange(
      panchangData: translatedPanchangData,
      context: context,
      currentTime: currentTime,
    );

    // Extract Sun/Moon component separately (first item is the combined component)
    final sunMoonComponent = staticItems.isNotEmpty ? staticItems[0] : null;

    // Extract Tithi Paksha component separately (first dynamic item)
    final tithiPakshaComponent = dynamicItems.isNotEmpty ? dynamicItems[0] : null;

    // Organize items by priority and grouping
    // Priority 1: Time-based and frequently checked items (excluding Sun/Moon and weekday)
    final priorityItems = [
      ...staticItems.skip(2), // Skip the Sun/Moon component and weekday widget
    ];

    // Priority 2: Current astrological states (excluding Tithi Paksha which is now separate)
    final currentStateItems = [
      // Tithi Paksha is now displayed separately above the grid
    ];

    // Priority 3: Other astrological elements (starting from index 1 since Tithi Paksha is separate)
    final otherItems = [
      if (dynamicItems.length > 1) dynamicItems[1], // Karana
      if (dynamicItems.length > 2) dynamicItems[2], // Yoga
      if (dynamicItems.length > 3) dynamicItems[3], // Maasa
      if (dynamicItems.length > 4) dynamicItems[4], // Samvatsara
      if (dynamicItems.length > 5) dynamicItems[5], // Nakshatra (combined card)
      if (dynamicItems.length > 6) dynamicItems[6], // Surya Nakshatra
    ];

    // Combine all items with priority ordering
    final allItems = [...priorityItems, ...currentStateItems, ...otherItems];



    // Main content with responsive grid
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color(0xFFF3E5AB), // Warm cream color
            Colors.white,
          ],
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          children: [
            // Main content - no scrolling, fixed height
            Expanded(
              child: Column(
                children: [
                  // Main Muhurta card (only if available) - enhanced styling
                  if (panchangSomeTimeChangeBox != null)
                    Container(
                      width: double.infinity,
                      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Colors.white, Color(0xFFFFFBF0)],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.08),
                            blurRadius: 10,
                            spreadRadius: 0,
                            offset: const Offset(0, 3),
                          ),
                        ],
                        border: Border.all(color: const Color(0xFFE8E8E8), width: 0.5),
                      ),
                      child: panchangSomeTimeChangeBox,
                    ),

                  // Sun and Moon component - full width above the grid
                  if (sunMoonComponent != null)
                    Container(
                      width: double.infinity,
                      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      child: sunMoonComponent,
                    ),

                  // Tithi Paksha component - full width like Sun/Moon
                  if (tithiPakshaComponent != null)
                    Container(
                      width: double.infinity,
                      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      child: tithiPakshaComponent,
                    ),

                  // Responsive grid of cards - takes remaining space
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 4.0),
                      child: LayoutBuilder(
                        builder: (context, constraints) {
                          // Mobile-optimized layout: 2 columns in portrait, 3 in landscape
                          final orientation = MediaQuery.of(context).orientation;
                          final crossAxisCount = orientation == Orientation.landscape ? 3 : 2;

                          // Calculate item size with mobile-optimized spacing
                          double itemWidth = (constraints.maxWidth / crossAxisCount) - 12;

                          return SingleChildScrollView(
                            child: Wrap(
                              spacing: 8.0,
                              runSpacing: 10.0,
                              alignment: WrapAlignment.center,
                              children: allItems.map((item) {
                                return Container(
                                  width: itemWidth,
                                  padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 6),
                                  decoration: BoxDecoration(
                                    gradient: const LinearGradient(
                                      colors: [Colors.white, Color(0xFFFFFDF7)],
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                    ),
                                    borderRadius: BorderRadius.circular(12.0),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withValues(alpha: 0.08),
                                        blurRadius: 8,
                                        spreadRadius: 0,
                                        offset: const Offset(0, 3),
                                      ),
                                    ],
                                    border: Border.all(color: const Color(0xFFE8E8E8), width: 0.5),
                                  ),
                                  child: item,
                                );
                              }).toList(),
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}